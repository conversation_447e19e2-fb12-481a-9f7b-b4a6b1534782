<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:controls="clr-namespace:Sanet.MakaMek.Avalonia.Controls"
             xmlns:templatedControls="clr-namespace:Sanet.MakaMek.Avalonia.Views.TemplatedControls"
             xmlns:converters="clr-namespace:Sanet.MakaMek.Avalonia.Converters"
             xmlns:behaviors="clr-namespace:Sanet.MakaMek.Avalonia.Behaviors"
             xmlns:viewModels1="clr-namespace:Sanet.MakaMek.Presentation.ViewModels;assembly=Sanet.MakaMek.Presentation"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="Sanet.MakaMek.Avalonia.Views.BattleMapView"
             x:CompileBindings="True"
             x:DataType="viewModels1:BattleMapViewModel">
    <UserControl.Resources>
        <converters:HitProbabilityColorConverter x:Key="HitProbabilityColorConverter"/>
        <converters:StringNotEmptyToBoolConverter x:Key="StringNotEmptyToBoolConverter"/>
    </UserControl.Resources>
    <Grid>
        <Canvas x:Name="MapCanvas" 
                Background="Transparent">
            <controls:DirectionSelector
                x:Name="DirectionSelector"
                IsVisible="{Binding IsDirectionSelectorVisible}"
                EnabledDirections="{Binding AvailableDirections}"
                Position="{Binding DirectionSelectorPosition}"
                DirectionSelectedCommand="{Binding DirectionSelectedCommand}"
                Foreground="{Binding ActivePlayerTint}"/>
        </Canvas>
        
        <Grid x:Name="TurnStatus"
            VerticalAlignment="Top"
            Background="{DynamicResource OverlayTransparentBrush}"
            Classes="statusPanel"
            Margin="{OnPlatform '0,0,0,0', Android='0,30,0,0'}"
            ColumnDefinitions="*,Auto,Auto,Auto,*">
            
            <!-- Turn Info Section -->
            <Border Grid.Column="1" 
                    Background="{Binding ActivePlayerTint, Converter={StaticResource HexColorToBrushConverter}}" 
                    Classes="statusSection"
                    Padding="10,5">
                <StackPanel Orientation="Horizontal" Classes="horizontalSpaced">
                    <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                        <TextBlock Text="TURN" 
                                 Classes="turnInfoLabel"
                                 HorizontalAlignment="Center"
                                 Foreground="{Binding ActivePlayerTint, Converter={StaticResource ContrastingForegroundConverter}}"/>
                        <TextBlock Text="{Binding Turn}" 
                                 Classes="turnInfo"
                                 FontWeight="Bold" 
                                 HorizontalAlignment="Center"
                                 Foreground="{Binding ActivePlayerTint, Converter={StaticResource ContrastingForegroundConverter}}"/>
                    </StackPanel>
                    
                    <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                        <TextBlock Text="PHASE" 
                                 Classes="turnInfoLabel"
                                 HorizontalAlignment="Center"
                                 Foreground="{Binding ActivePlayerTint, Converter={StaticResource ContrastingForegroundConverter}}"/>
                        <TextBlock Text="{Binding TurnPhaseName}" 
                                 Classes="turnInfo"
                                 FontWeight="Bold" 
                                 HorizontalAlignment="Center"
                                 Foreground="{Binding ActivePlayerTint, Converter={StaticResource ContrastingForegroundConverter}}"/>
                    </StackPanel>
                </StackPanel>
            </Border>
            
            <!-- Active Player Section -->
            <Border Grid.Column="2" 
                    Background="{Binding ActivePlayerTint, Converter={StaticResource HexColorToBrushConverter}}" 
                    Classes="statusSection"
                    Padding="10,5"
                    Margin="1,0,1,0">
                <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                    <TextBlock Text="ACTIVE PLAYER" 
                             Classes="turnInfoLabel"
                             HorizontalAlignment="Center"
                             Foreground="{Binding ActivePlayerTint, Converter={StaticResource ContrastingForegroundConverter}}"/>
                    <TextBlock Text="{Binding ActivePlayerName}" 
                             Classes="turnInfo"
                             FontWeight="Bold" 
                             HorizontalAlignment="Center"
                             Foreground="{Binding ActivePlayerTint, Converter={StaticResource ContrastingForegroundConverter}}"/>
                </StackPanel>
            </Border>
            
            <!-- Player Action Section -->
            <Border Grid.Column="3" 
                    Background="{Binding ActivePlayerTint, Converter={StaticResource HexColorToBrushConverter}}" 
                    Classes="statusSection"
                    Padding="15,5"
                    IsVisible="{Binding IsUserActionLabelVisible}">
                <TextBlock Text="{Binding ActionInfoLabel}" 
                         Classes="actionInfo"
                         TextWrapping="Wrap"
                         FontWeight="Bold"
                         Foreground="{Binding ActivePlayerTint, Converter={StaticResource ContrastingForegroundConverter}}"
                         VerticalAlignment="Center"/>
            </Border>
        </Grid>
        
        <Grid x:Name="MechList"
              Background="{DynamicResource PrimaryBrush}"
              IsVisible="{Binding AreUnitsToDeployVisible}"
              Grid.RowDefinitions="Auto,*"
              HorizontalAlignment="Center"
              VerticalAlignment="Center"
              Classes="card">
            <Label Content="Select a Unit to deploy" Foreground="White"/>
            <ListBox Grid.Row="1"
                Classes="gameList"
                ItemsSource="{Binding UnitsToDeploy}"
                SelectedItem="{Binding SelectedUnit}">
                <ListBox.ItemTemplate>
                    <DataTemplate>
                        <TextBlock Text="{Binding Name}" Classes="body"/>
                    </DataTemplate>
                </ListBox.ItemTemplate>
            </ListBox>
        </Grid>
        
        <controls:WeaponSelectionPanel
            x:Name="WeaponSelectionPanel"
            HorizontalAlignment="Right"
            VerticalAlignment="Center"/>
        
        <StackPanel HorizontalAlignment="Right"
                    VerticalAlignment="Bottom"
                    Margin="10"
                    Spacing="10">
            <!-- Record Sheet Button -->
            <templatedControls:ActionButton
                IsVisible="{Binding IsRecordSheetButtonVisible}"
                Command="{Binding ToggleRecordSheet}"
                Background="Aqua"
                IconData="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6zm-1 1v5h5v10H6V3h7z"/>

            <!-- Command Log Button -->
            <templatedControls:ActionButton
                IsVisible="{Binding !IsCommandLogExpanded}"
                Command="{Binding ToggleCommandLog}"
                Background="Aqua"
                IconData="M8 14v-4m4 4v-4m4 4v-4M3 21h18V7H3v14zM3 7V5a2 2 0 012-2h14a2 2 0 012 2v2H3z"/>
        </StackPanel>

        <!-- Player Action Button -->
        <Button HorizontalAlignment="Center"
                VerticalAlignment="Bottom"
                Margin="0,0,0,20"
                Padding="15,10"
                Command="{Binding HandlePlayerAction}"
                IsVisible="{Binding IsPlayerActionButtonVisible}"
                Background="{Binding ActivePlayerTint, Converter={StaticResource HexColorToBrushConverter}}"
                Foreground="{Binding ActivePlayerTint, Converter={StaticResource ContrastingForegroundConverter}}">
            <TextBlock Text="{Binding PlayerActionLabel}" 
                     FontSize="16" 
                     FontWeight="Bold"/>
        </Button>

        <!-- Record Sheet Panel -->
        <templatedControls:GamePanel
            IsVisible="{Binding IsRecordSheetPanelVisible}"
            Title="{Binding SelectedUnit.Name}"
            CloseCommand="{Binding ToggleRecordSheet}">
            <TabControl>
                <TabItem>
                    <TabItem.Header>
                        <TextBlock Classes="h3" FontFamily="{StaticResource AwesomeFontSolid}" Text="&#xf05a;"/>
                    </TabItem.Header>
                    <StackPanel Margin="10">
                        <controls:UnitBasicInfoPanel DataContext="{Binding SelectedUnit}" />
                        <controls:UnitHeatLevelPanel DataContext="{Binding SelectedUnit}" />
                        <controls:UnitMovementInfoPanel DataContext="{Binding SelectedUnit}" />
                    </StackPanel>
                </TabItem>
                <TabItem>
                    <TabItem.Header>
                        <TextBlock Classes="h3" FontFamily="{StaticResource AwesomeFontSolid}" Text="&#xf007;"/>
                    </TabItem.Header>
                    <controls:UnitPilotInfoPanel DataContext="{Binding SelectedUnit.Pilot}" />
                </TabItem>
                <TabItem>
                    <TabItem.Header>
                        <TextBlock Classes="h3" FontFamily="{StaticResource AwesomeFontSolid}" Text="&#xf132;"/>
                    </TabItem.Header>
                    <controls:UnitHealthIndicator Unit="{Binding SelectedUnit}"/>
                </TabItem>
                <TabItem>
                    <TabItem.Header>
                        <TextBlock Classes="h3" FontFamily="{StaticResource AwesomeFontSolid}" Text="&#x26a1;"/>
                    </TabItem.Header>
                    <controls:UnitWeaponsPanel Unit="{Binding SelectedUnit}"/>
                </TabItem>
                <TabItem>
                    <TabItem.Header>
                        <TextBlock Classes="h3" FontFamily="{StaticResource AwesomeFontSolid}" Text="&#xf085;"/>
                    </TabItem.Header>
                    <controls:UnitComponentsPanel Unit="{Binding SelectedUnit}" />
                </TabItem>
                <TabItem>
                    <TabItem.Header>
                        <TextBlock Classes="h3" FontFamily="{StaticResource AwesomeFontSolid}" Text="&#xf0e0;"/>
                    </TabItem.Header>
                    <controls:UnitEventsPanel/>
                </TabItem>
            </TabControl>
        </templatedControls:GamePanel>

        <!-- Command Log Panel -->
        <templatedControls:GamePanel
            IsVisible="{Binding IsCommandLogExpanded}"
            Title="Command Log"
            CloseCommand="{Binding ToggleCommandLog}">
            <ScrollViewer Grid.Row="1" 
                          MaxHeight="360">
                <Interaction.Behaviors>
                    <behaviors:AutoScrollBehavior />
                </Interaction.Behaviors>
                <ItemsControl ItemsSource="{Binding CommandLog}">
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding}"
                                       Foreground="Black"
                                       Margin="5"
                                       TextWrapping="Wrap"/>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </ScrollViewer>
        </templatedControls:GamePanel>
    </Grid>
</UserControl>

